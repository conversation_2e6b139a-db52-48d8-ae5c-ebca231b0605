using DeepMessage.Client.Common.Data;
using Platform.Client.Common.Features.Friends;
using DeepMessage.MauiShared;
using DeepMessage.ServiceContracts.Features.Friends;
using System.Security.Claims;
using System.Text.Json;
using Platform.Client.Services.Features.Friends;
using Platform.Framework.Core;
namespace Platform.Client.Common.Features.Friends;
public class FriendFormViewBase : FormBaseMaui<FriendFormBusinessObject, FriendFormViewModel, string, IFriendFormDataService>
{
    public FriendFormViewBase(IServiceScopeFactory scopeFactory, string key) : base(scopeFactory, key)
    {
    }

    public override async Task OnAfterSaveAsync(string key)
    {
        try
        {
            //lets try pulling from the server
            using var scope = ScopeFactory.CreateScope();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();
            var localStorage = scope.ServiceProvider.GetRequiredService<ILocalStorageService>();

            // Deserialize the new FriendFormResponseDto format
            var friendResponse = JsonSerializer.Deserialize<FriendFormResponseDto>(key) ?? throw new InvalidDataException("Friend data is null");

            // Convert to Friendship entity for local storage
            var friendship = new Friendship
            {
                Id = friendResponse.Id,
                UserId = await localStorage.GetValue(ClaimTypes.NameIdentifier) ?? string.Empty,
                FriendId = friendResponse.FriendId,
                Name = friendResponse.Name,
                //DisplayPictureUrl = friendResponse.DisplayPictureUrl,
                Pub1 = friendResponse.Pub1, // Store friend's public key for encryption
                CreatedAt = friendResponse.CreatedAt
            };

            if (!context.Friendships.Any(x => x.Id == friendship.Id))
            {
                context.Friendships.Add(friendship);
                await context.SaveChangesAsync();
            }
            await MainThread.InvokeOnMainThreadAsync(async () =>
              {
                  // Animate exit before closing modal
                  if (this is FriendFormView friendFormView)
                  {
                      await friendFormView.AnimateExit();
                  }
                  await Navigation.PopModalAsync();
              });
        }
        catch (Exception)
        {

        }

    }
}

public partial class FriendFormView : FriendFormViewBase
{
    public FriendFormView(IServiceScopeFactory scopeFactory, string key) : base(scopeFactory, key)
    {
        InitializeComponent();
        BindingContext = this;

        // Set initial state for entrance animation
        var mainBorder = this.FindByName<Border>("MainBorder");
        if (mainBorder != null)
        {
            this.Opacity = 0;
            mainBorder.Scale = 0.9;
            //mainBorder.TranslationY = 30;
        }
    }

    protected override async void OnAppearing()
    {
        base.OnAppearing();
        // Animate entrance
        await AnimateEntrance();
    }

    /// <summary>
    /// Animates the entrance of the add friend modal
    /// </summary>
    private async Task AnimateEntrance()
    {
        try
        {
            var mainBorder = this.FindByName<Border>("MainBorder");
            if (mainBorder != null)
            {
                // Small delay to ensure the page is loaded
                await Task.Delay(100);

                // Animate entrance with fade, scale, and slide up
                var fadeTask1 = this.FadeTo(1, 500, Easing.CubicOut);
                var scaleTask = mainBorder.ScaleTo(1, 500, Easing.CubicOut);
                //var translateTask = mainBorder.TranslateTo(0, 0, 500, Easing.CubicOut);

                await Task.WhenAll(fadeTask1, scaleTask);
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Entrance animation failed: {ex.Message}");
            // Ensure visibility if animation fails
            var mainBorder = this.FindByName<Border>("MainBorder");
            if (mainBorder != null)
            {
                mainBorder.Opacity = 1;
                mainBorder.Scale = 1;
                mainBorder.TranslationY = 0;
            }
        }
    }

    /// <summary>
    /// Animates the exit of the add friend modal
    /// </summary>
    public async Task AnimateExit()
    {
        try
        {
            var mainBorder = this.FindByName<Border>("MainBorder");
            if (mainBorder != null)
            {
                // Animate the main content with exit effects
                var fadeTask = mainBorder.FadeTo(0, 400, Easing.CubicOut);
                var scaleTask = mainBorder.ScaleTo(0.9, 400, Easing.CubicOut);
                var translateTask = mainBorder.TranslateTo(0, 50, 400, Easing.CubicOut);

                // Also fade the background
                var backgroundFadeTask = this.FadeTo(0.3, 400, Easing.CubicOut);

                // Wait for all animations to complete
                await Task.WhenAll(fadeTask, scaleTask, translateTask, backgroundFadeTask);
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Exit animation failed: {ex.Message}");
        }
    }

    private void Button_Clicked(object sender, EventArgs e)
    {
        Navigation.PopModalAsync();
    }
}
