<?xml version="1.0" encoding="utf-8" ?>
<ContentPage
    x:Class="Platform.Client.Common.Features.Settings.SettingsView"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:local="clr-namespace:Platform.Client.Common.Features.Settings"
    Title="Settings"
    BackgroundColor="{AppThemeBinding Light={StaticResource White},
                                      Dark={StaticResource Gray800}}"
    Shell.BackgroundColor="{AppThemeBinding Light={StaticResource White},
                                            Dark={StaticResource Gray800}}"
    Shell.TitleColor="{AppThemeBinding Light={StaticResource Gray800},
                                       Dark={StaticResource White}}">

    <ScrollView>
        <VerticalStackLayout Padding="16" Spacing="24">

            <!-- Header Section -->
            <VerticalStackLayout Spacing="8">
                <Border
                    Background="{AppThemeBinding Light={StaticResource Gray100},
                                                 Dark={StaticResource Gray600}}"
                    HeightRequest="64"
                    HorizontalOptions="Center"
                    Stroke="{AppThemeBinding Light={StaticResource Gray400},
                                             Dark={StaticResource Gray500}}"
                    WidthRequest="64">
                    <Border.StrokeShape>
                        <RoundRectangle CornerRadius="32" />
                    </Border.StrokeShape>
                    <Image
                        HeightRequest="32"
                        HorizontalOptions="Center"
                        VerticalOptions="Center">
                        <Image.Source>
                            <FontImageSource
                                FontFamily="Jelly"
                                Glyph="&#xf1de;"
                                Size="32"
                                Color="{AppThemeBinding Light={StaticResource Gray700},
                                                        Dark={StaticResource Gray300}}" />
                        </Image.Source>
                    </Image>
                </Border>
                <Label
                    FontAttributes="Bold"
                    FontSize="24"
                    HorizontalOptions="Center"
                    Text="App Settings"
                    TextColor="{AppThemeBinding Light={StaticResource Gray800},
                                                Dark={StaticResource White}}" />
            </VerticalStackLayout>

            <!-- Startup Settings Section -->
            <VerticalStackLayout Spacing="16">
                <Label
                    FontAttributes="Bold"
                    FontSize="18"
                    Text="Startup"
                    TextColor="{AppThemeBinding Light={StaticResource Gray700},
                                                Dark={StaticResource Gray300}}" />

                <!-- Show Captcha on Startup -->
                <Border
                    Background="{AppThemeBinding Light={StaticResource Gray50},
                                                 Dark={StaticResource Gray700}}"
                    Padding="12"
                    Stroke="{AppThemeBinding Light={StaticResource Gray100},
                                             Dark={StaticResource Gray500}}">
                    <Border.StrokeShape>
                        <RoundRectangle CornerRadius="8" />
                    </Border.StrokeShape>
                    <Grid ColumnDefinitions="Auto,*,Auto" ColumnSpacing="12">
                        <Image Grid.Column="0" VerticalOptions="Center">
                            <Image.Source>
                                <FontImageSource
                                    FontFamily="Jelly"
                                    Glyph="&#xf023;"
                                    Size="20"
                                    Color="{AppThemeBinding Light={StaticResource Gray700},
                                                            Dark={StaticResource Gray300}}" />
                            </Image.Source>
                        </Image>
                        <VerticalStackLayout Grid.Column="1" Spacing="4" VerticalOptions="Center">
                            <Label
                                FontAttributes="Bold"
                                FontSize="16"
                                Text="Show Captcha on Startup"
                                TextColor="{AppThemeBinding Light={StaticResource Gray800},
                                                            Dark={StaticResource White}}" />
                            <Label
                                FontSize="14"
                                Text="Require captcha verification when app starts"
                                TextColor="{AppThemeBinding Light={StaticResource Gray600},
                                                            Dark={StaticResource Gray400}}" />
                        </VerticalStackLayout>
                        <Switch
                            Grid.Column="2"
                            IsToggled="{Binding ShowCaptchaOnStartup}"
                            VerticalOptions="Center" />
                    </Grid>
                </Border>

                <!-- Secret Code Entry -->
                <Border
                    Background="{AppThemeBinding Light={StaticResource Gray50},
                                                 Dark={StaticResource Gray700}}"
                    Padding="12"
                    Stroke="{AppThemeBinding Light={StaticResource Gray100},
                                             Dark={StaticResource Gray500}}">
                    <Border.StrokeShape>
                        <RoundRectangle CornerRadius="8" />
                    </Border.StrokeShape>
                    <Grid ColumnDefinitions="Auto,*" ColumnSpacing="12">
                        <Image Grid.Column="0" VerticalOptions="Center">
                            <Image.Source>
                                <FontImageSource
                                    FontFamily="Jelly"
                                    Glyph="&#xf084;"
                                    Size="20"
                                    Color="{AppThemeBinding Light={StaticResource Gray700},
                                                            Dark={StaticResource Gray300}}" />
                            </Image.Source>
                        </Image>
                        <VerticalStackLayout Grid.Column="1" Spacing="8">
                            <Label
                                FontAttributes="Bold"
                                FontSize="16"
                                Text="Secret Code to Show Captcha"
                                TextColor="{AppThemeBinding Light={StaticResource Gray800},
                                                            Dark={StaticResource White}}" />
                            <Entry
                                FontSize="16"
                                IsPassword="True"
                                Placeholder="Enter secret code..."
                                PlaceholderColor="{AppThemeBinding Light={StaticResource Gray400},
                                                                   Dark={StaticResource Gray600}}"
                                Text="{Binding SecretCode}"
                                TextColor="{AppThemeBinding Light={StaticResource Gray700},
                                                            Dark={StaticResource Gray300}}" />
                        </VerticalStackLayout>
                    </Grid>
                </Border>
            </VerticalStackLayout>

            <!-- Appearance Settings Section -->
            <VerticalStackLayout Spacing="16">
                <Label
                    FontAttributes="Bold"
                    FontSize="18"
                    Text="Appearance"
                    TextColor="{AppThemeBinding Light={StaticResource Gray700},
                                                Dark={StaticResource Gray300}}" />

                <!-- App Theme Switch -->
                <Border
                    Background="{AppThemeBinding Light={StaticResource Gray50},
                                                 Dark={StaticResource Gray700}}"
                    Padding="12"
                    Stroke="{AppThemeBinding Light={StaticResource Gray100},
                                             Dark={StaticResource Gray500}}">
                    <Border.StrokeShape>
                        <RoundRectangle CornerRadius="8" />
                    </Border.StrokeShape>
                    <Grid ColumnDefinitions="Auto,*,Auto" ColumnSpacing="12">
                        <Image Grid.Column="0" VerticalOptions="Center">
                            <Image.Source>
                                <FontImageSource
                                    FontFamily="Jelly"
                                    Glyph="&#xf186;"
                                    Size="20"
                                    Color="{AppThemeBinding Light={StaticResource Gray700},
                                                            Dark={StaticResource Gray300}}" />
                            </Image.Source>
                        </Image>
                        <VerticalStackLayout Grid.Column="1" Spacing="4" VerticalOptions="Center">
                            <Label
                                FontAttributes="Bold"
                                FontSize="16"
                                Text="Dark Theme"
                                TextColor="{AppThemeBinding Light={StaticResource Gray800},
                                                            Dark={StaticResource White}}" />
                            <Label
                                FontSize="14"
                                Text="Switch between light and dark appearance"
                                TextColor="{AppThemeBinding Light={StaticResource Gray600},
                                                            Dark={StaticResource Gray400}}" />
                        </VerticalStackLayout>
                        <Switch
                            Grid.Column="2"
                            IsToggled="{Binding IsDarkTheme}"
                            VerticalOptions="Center" />
                    </Grid>
                </Border>
            </VerticalStackLayout>

            <!-- Notification Settings Section -->
            <VerticalStackLayout Spacing="16">
                <Label
                    FontAttributes="Bold"
                    FontSize="18"
                    Text="Notifications"
                    TextColor="{AppThemeBinding Light={StaticResource Gray700},
                                                Dark={StaticResource Gray300}}" />

                <!-- Sound Notifications Switch -->
                <Border
                    Background="{AppThemeBinding Light={StaticResource Gray50},
                                                 Dark={StaticResource Gray700}}"
                    Padding="12"
                    Stroke="{AppThemeBinding Light={StaticResource Gray100},
                                             Dark={StaticResource Gray500}}">
                    <Border.StrokeShape>
                        <RoundRectangle CornerRadius="8" />
                    </Border.StrokeShape>
                    <Grid ColumnDefinitions="Auto,*,Auto" ColumnSpacing="12">
                        <Image Grid.Column="0" VerticalOptions="Center">
                            <Image.Source>
                                <FontImageSource
                                    FontFamily="Jelly"
                                    Glyph="&#xf027;"
                                    Size="20"
                                    Color="{AppThemeBinding Light={StaticResource Gray700},
                                                            Dark={StaticResource Gray300}}" />
                            </Image.Source>
                        </Image>
                        <VerticalStackLayout Grid.Column="1" Spacing="4" VerticalOptions="Center">
                            <Label
                                FontAttributes="Bold"
                                FontSize="16"
                                Text="Play Sound for Notifications"
                                TextColor="{AppThemeBinding Light={StaticResource Gray800},
                                                            Dark={StaticResource White}}" />
                            <Label
                                FontSize="14"
                                Text="Enable audio alerts for new messages"
                                TextColor="{AppThemeBinding Light={StaticResource Gray600},
                                                            Dark={StaticResource Gray400}}" />
                        </VerticalStackLayout>
                        <Switch
                            Grid.Column="2"
                            IsToggled="{Binding PlaySoundForNotifications}"
                            VerticalOptions="Center" />
                    </Grid>
                </Border>
            </VerticalStackLayout>

        </VerticalStackLayout>
    </ScrollView>
</ContentPage>
