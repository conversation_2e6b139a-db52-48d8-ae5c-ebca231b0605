﻿using DeepMessage.Client.Common.Data;
using DeepMessage.MauiShared;
using DeepMessage.ServiceContracts.Features.Account;
using Java.Time;
using Microsoft.Extensions.DependencyInjection;
using MobileApp.MauiShared;
using ModelFury.Briefly.MobileApp.Features.Account;
using Platform.Client.Services;
using Platform.Client.Services.Services;
using Platform.Framework.Core;
using System.Collections.ObjectModel;
using System.Security.Claims;
using System.Text.Json;
using System.Windows.Input;

namespace ModelFury.Briefly.MobileApp.Features.Account;

public class SignupFormViewBase : FormBaseMaui<SignupFormBusinessObject, SignupFormViewModel, string, ISignupFormDataService>
{
    public SignupFormViewBase(IServiceScopeFactory scopeFactory, string key) : base(scopeFactory, key)
    {
    }
}

public partial class SignupFormView : SignupFormViewBase
{

    private ObservableCollection<string> avatarOptions;
    public ObservableCollection<string> AvatarOptions
    {
        get => avatarOptions;
        set
        {
            avatarOptions = value;
            OnPropertyChanged();
        }
    }

    public ICommand TogglePasswordVisibilityCommand { get; }
    public ICommand GoToSigninCommand { get; }

    //public override Color TitleBarColor => Color.FromArgb("#004f98");

    public SignupFormView(IServiceScopeFactory scopeFactory) : base(scopeFactory, null!)
    {
        InitializeComponent();
        TogglePasswordVisibilityCommand = new Command(TogglePasswordVisibility);
        GoToSigninCommand = new Command(async () => await GoToSignin());
        avatarOptions = [];
        for (int i = 1; i <= 12; i++)
        {
            avatarOptions.Add($"a{i}.png");
        }
        BindingContext = this;
    }


    protected override async Task<SignupFormViewModel> CreateSelectedItem()
    {
        using var scope = ScopeFactory.CreateScope();
        var storageService = scope.ServiceProvider.GetRequiredService<ILocalStorageService>();
        return new SignupFormViewModel()
        {
            DeviceString = $"{DeviceInfo.Manufacturer}-{DeviceInfo.Model}-{DeviceInfo.Platform}-{DeviceInfo.VersionString}",
            ShowPassword = false,
            ReferralCode = await storageService.GetValue("code") ?? string.Empty,
            AvatarData = string.Empty,
            DisplayName = string.Empty,
            NickName = string.Empty,
            PassKey = string.Empty
        };
    }

    protected override SignupFormBusinessObject ConvertViewModelToBusinessModel(SignupFormViewModel formViewModel)
    {
        var scope = ScopeFactory.CreateScope();
        var _encryptionService = scope.ServiceProvider.GetRequiredService<IClientEncryptionService>();
        var _secureKeyManager = scope.ServiceProvider.GetRequiredService<ISecureKeyManager>();

        // Step 1: Generate RSA 2048-bit key pair on client-side
        var keyPair = _encryptionService.GenerateRSAKeyPairAsync();

        // Step 2: Derive AES-256 key from PassKey using username as deterministic salt
        var aesKey = _secureKeyManager.DeriveTemporaryAESKeyAsync(formViewModel.NickName!, formViewModel.PassKey!);

        // Step 3: Encrypt RSA private key with derived AES key
        var encryptedPrivateKey = _encryptionService.EncryptRSAPrivateKeyAsync(keyPair.PrivateKeyPem, aesKey);

        //await _localStorageService.SetValue(formBusinessObject.NickName!, ClaimTypes.Name);
        //await _localStorageService.SetValue(encryptedPrivateKey, "pub2e_");
        //await _localStorageService.SetValue(keyPair.PublicKeyPem, "pub1o_");


        var passKey = formViewModel.PassKey.Trim();
        if (!string.IsNullOrEmpty(formViewModel.NickName))
        {
            passKey = $"{formViewModel.NickName}{_encryptionService.GenerateBitSignature(SelectedItem.PassKey!)}!";
        }
        ;

        var signupBusinessObject = new SignupFormBusinessObject()
        {
            NickName = formViewModel.NickName.Trim(),
            DeviceString = formViewModel.DeviceString,
            DisplayName = formViewModel.DisplayName?.Trim(),
            AvatarData = formViewModel.AvatarData,
            PassKey = passKey,
            ReferralCode = formViewModel.ReferralCode,
            Pub1 = keyPair.PublicKeyPem,
            Pub2 = encryptedPrivateKey
        };

        // Clear sensitive data from local variables
        Array.Clear(aesKey, 0, aesKey.Length);

        return signupBusinessObject;
    }


    private void TogglePasswordVisibility()
    {
        if (SelectedItem != null)
        {
            SelectedItem.ShowPassword = !SelectedItem.ShowPassword;
            OnPropertyChanged(nameof(SelectedItem));
        }
    }

    private async Task GoToSignin()
    {
        await Shell.Current.GoToAsync("//signin");
        await Navigation.PopToRootAsync();
    }



    public override async Task OnAfterSaveAsync(string key)
    {
        using var scope = ScopeFactory.CreateScope();
        var storageService = scope.ServiceProvider.GetRequiredService<ILocalStorageService>();
        var _secureKeyManager = scope.ServiceProvider.GetRequiredService<ISecureKeyManager>();

        var authClaims = JsonSerializer.Deserialize<AuthorizationClaimsModel>(key);
        await storageService.SetValue(authClaims.Token, "auth_token");
        await storageService.SetValue(authClaims.RefreshToken, "refresh_token");
        await storageService.SetValue(authClaims.UserId, ClaimTypes.NameIdentifier);
        await storageService.SetValue(authClaims.Username, ClaimTypes.Name);

        await storageService.SetValue(authClaims.Pub1, "pub1o_");
        await storageService.SetValue(authClaims.Pub2, "pub2e_");

        var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();
        var user = await context.ApplicationUsers.FindAsync(authClaims.UserId);
        if (user == null)
        {
            user = new ApplicationUser()
            {
                Id = authClaims.UserId,
                NickName = authClaims.Username,
                AvatarData = SelectedItem.AvatarData,
                DisplayName = SelectedItem.DisplayName,
                Pub1 = SelectedItem.Pub1,
                Pub2 = SelectedItem.Pub2,
                Hash = "dummy",
            };
            context.ApplicationUsers.Add(user);
            await context.SaveChangesAsync();
        }

        await _secureKeyManager.DeriveAndStoreKeysAsync(SelectedItem.NickName!, SelectedItem.PassKey!);

        await Shell.Current.GoToAsync("//messages");
        await Navigation.PopToRootAsync();
    }
}
