﻿<?xml version="1.0" encoding="utf-8" ?>
<local:AuthCodeListingViewBase
    x:Class="Platform.Client.Common.Features.AuthCodes.AuthCodeListingView"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:local="clr-namespace:Platform.Client.Common.Features.AuthCodes"
    xmlns:authcodes="clr-namespace:Platform.Client.Services.Features.AuthCodes;assembly=Platform.Client.Services"
    x:DataType="local:AuthCodeListingView"
    Title="AuthCodeListingView">

    <ContentPage.ToolbarItems>
        <ToolbarItem Command="{Binding SyncDownItemsCommand}" IconImageSource="plus_light.png" />
    </ContentPage.ToolbarItems>
    <CollectionView x:Name="collection" ItemsSource="{Binding Items}">
        <CollectionView.ItemTemplate>
            <DataTemplate x:DataType="authcodes:AuthCodeListingViewModel">
                <HorizontalStackLayout>
                    <Label Margin="16,8" Text="{Binding AuthCode}" />
                    <Label Margin="16,8" Text="{Binding CreatedAt}" />
                    <Label Margin="4,8" Text="{Binding AuthCodeStatus}" />
                </HorizontalStackLayout>
            </DataTemplate>
        </CollectionView.ItemTemplate>
    </CollectionView>
</local:AuthCodeListingViewBase>
